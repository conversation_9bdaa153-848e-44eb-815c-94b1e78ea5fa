"""
Word文档内容读取核心功能模块

提供Word文档的各种读取功能，包括文档信息获取、文本提取、大纲获取等。
"""

import os
import json
from typing import Dict, List, Any
from docx import Document


def ensure_docx_extension(filename: str) -> str:
    """确保文件名有.docx扩展名"""
    if not filename.endswith('.docx'):
        filename += '.docx'
    return filename


def get_document_properties(doc_path: str) -> Dict[str, Any]:
    """获取Word文档的属性信息"""
    if not os.path.exists(doc_path):
        return {"error": f"Document {doc_path} does not exist"}
    
    try:
        doc = Document(doc_path)
        core_props = doc.core_properties
        
        return {
            "title": core_props.title or "",
            "author": core_props.author or "",
            "subject": core_props.subject or "",
            "keywords": core_props.keywords or "",
            "created": str(core_props.created) if core_props.created else "",
            "modified": str(core_props.modified) if core_props.modified else "",
            "last_modified_by": core_props.last_modified_by or "",
            "revision": core_props.revision or 0,
            "page_count": len(doc.sections),
            "word_count": sum(len(paragraph.text.split()) for paragraph in doc.paragraphs),
            "paragraph_count": len(doc.paragraphs),
            "table_count": len(doc.tables)
        }
    except Exception as e:
        return {"error": f"Failed to get document properties: {str(e)}"}


def extract_document_text(doc_path: str) -> str:
    """从Word文档中提取所有文本"""
    if not os.path.exists(doc_path):
        return f"Document {doc_path} does not exist"
    
    try:
        doc = Document(doc_path)
        text = []
        
        for paragraph in doc.paragraphs:
            text.append(paragraph.text)
            
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        text.append(paragraph.text)
        
        return "\n".join(text)
    except Exception as e:
        return f"Failed to extract text: {str(e)}"


def get_document_structure(doc_path: str) -> Dict[str, Any]:
    """获取Word文档的结构信息"""
    if not os.path.exists(doc_path):
        return {"error": f"Document {doc_path} does not exist"}
    
    try:
        doc = Document(doc_path)
        structure = {
            "paragraphs": [],
            "tables": []
        }
        
        # 获取段落信息
        for i, para in enumerate(doc.paragraphs):
            structure["paragraphs"].append({
                "index": i,
                "text": para.text[:100] + ("..." if len(para.text) > 100 else ""),
                "style": para.style.name if para.style else "Normal"
            })
        
        # 获取表格信息
        for i, table in enumerate(doc.tables):
            table_data = {
                "index": i,
                "rows": len(table.rows),
                "columns": len(table.columns),
                "preview": []
            }
            
            # 获取表格数据样本
            max_rows = min(3, len(table.rows))
            for row_idx in range(max_rows):
                row_data = []
                max_cols = min(3, len(table.columns))
                for col_idx in range(max_cols):
                    try:
                        cell_text = table.cell(row_idx, col_idx).text
                        row_data.append(cell_text[:20] + ("..." if len(cell_text) > 20 else ""))
                    except IndexError:
                        row_data.append("N/A")
                table_data["preview"].append(row_data)
            
            structure["tables"].append(table_data)
        
        return structure
    except Exception as e:
        return {"error": f"Failed to get document structure: {str(e)}"}


def get_paragraph_text(doc_path: str, paragraph_index: int) -> Dict[str, Any]:
    """获取指定段落的文本内容"""
    if not os.path.exists(doc_path):
        return {"error": f"Document {doc_path} does not exist"}
    
    try:
        doc = Document(doc_path)
        
        # 检查段落索引是否有效
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return {"error": f"Invalid paragraph index: {paragraph_index}. Document has {len(doc.paragraphs)} paragraphs."}
        
        paragraph = doc.paragraphs[paragraph_index]
        
        return {
            "index": paragraph_index,
            "text": paragraph.text,
            "style": paragraph.style.name if paragraph.style else "Normal",
            "is_heading": paragraph.style.name.startswith("Heading") if paragraph.style else False
        }
    except Exception as e:
        return {"error": f"Failed to get paragraph text: {str(e)}"}


def find_text(doc_path: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> Dict[str, Any]:
    """在Word文档中查找指定文本的所有出现位置"""
    if not os.path.exists(doc_path):
        return {"error": f"Document {doc_path} does not exist"}
    
    if not text_to_find:
        return {"error": "Search text cannot be empty"}
    
    try:
        doc = Document(doc_path)
        results = {
            "query": text_to_find,
            "match_case": match_case,
            "whole_word": whole_word,
            "occurrences": [],
            "total_count": 0
        }
        
        search_text = text_to_find if match_case else text_to_find.lower()
        
        for i, para in enumerate(doc.paragraphs):
            para_text = para.text if match_case else para.text.lower()
            
            # 查找所有出现位置
            start_pos = 0
            while True:
                if whole_word:
                    # 全词匹配搜索
                    words = para_text.split()
                    found = False
                    for word_idx, word in enumerate(words):
                        if (word == search_text or 
                            (not match_case and word.lower() == search_text.lower())):
                            results["occurrences"].append({
                                "paragraph_index": i,
                                "position": word_idx,
                                "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                            })
                            results["total_count"] += 1
                            found = True
                    
                    # 检查完所有单词后跳出
                    break
                else:
                    # 子字符串搜索
                    pos = para_text.find(search_text, start_pos)
                    if pos == -1:
                        break
                    
                    results["occurrences"].append({
                        "paragraph_index": i,
                        "position": pos,
                        "context": para.text[:100] + ("..." if len(para.text) > 100 else "")
                    })
                    results["total_count"] += 1
                    start_pos = pos + 1
        
        return results
    except Exception as e:
        return {"error": f"Failed to search text: {str(e)}"}


# 主要的API函数

def get_document_info(filename: str) -> str:
    """获取Word文档的信息
    
    Args:
        filename: Word文档的路径
        
    Returns:
        JSON格式的文档信息字符串
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"Document {filename} does not exist"
    
    try:
        properties = get_document_properties(filename)
        return json.dumps(properties, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"Failed to get document info: {str(e)}"


def get_document_text(filename: str) -> str:
    """提取Word文档中的所有文本
    
    Args:
        filename: Word文档的路径
        
    Returns:
        文档的文本内容
    """
    filename = ensure_docx_extension(filename)
    
    return extract_document_text(filename)


def get_document_outline(filename: str) -> str:
    """获取Word文档的结构大纲
    
    Args:
        filename: Word文档的路径
        
    Returns:
        JSON格式的文档结构字符串
    """
    filename = ensure_docx_extension(filename)
    
    structure = get_document_structure(filename)
    return json.dumps(structure, indent=2, ensure_ascii=False)


def get_paragraph_text_from_document(filename: str, paragraph_index: int) -> str:
    """从Word文档中获取指定段落的文本
    
    Args:
        filename: Word文档的路径
        paragraph_index: 段落索引（从0开始）
        
    Returns:
        JSON格式的段落信息字符串
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"Document {filename} does not exist"
    
    if paragraph_index < 0:
        return "Invalid parameter: paragraph_index must be a non-negative integer"
    
    try:
        result = get_paragraph_text(filename, paragraph_index)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"Failed to get paragraph text: {str(e)}"


def find_text_in_document(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> str:
    """在Word文档中查找指定文本
    
    Args:
        filename: Word文档的路径
        text_to_find: 要查找的文本
        match_case: 是否区分大小写（True）或忽略大小写（False）
        whole_word: 是否只匹配完整单词（True）或匹配子字符串（False）
        
    Returns:
        JSON格式的搜索结果字符串
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"Document {filename} does not exist"
    
    if not text_to_find:
        return "Search text cannot be empty"
    
    try:
        result = find_text(filename, text_to_find, match_case, whole_word)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"Failed to find text: {str(e)}"

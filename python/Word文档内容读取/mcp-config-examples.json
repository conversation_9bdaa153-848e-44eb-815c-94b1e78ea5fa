{"_comment": "Word文档内容读取MCP服务配置示例", "_description": "选择其中一种配置方式添加到Claude Desktop配置文件中", "配置方式1_使用uv_推荐": {"mcpServers": {"word-document-reader": {"name": "Word文档内容读取", "type": "stdio", "description": "提供Word文档内容读取相关功能", "isActive": true, "registryUrl": "", "command": "uv", "args": ["--directory", "/Users/<USER>/Cursor/skills/python/Word文档内容读取", "run", "python", "-m", "word_document_reader.main"]}}}, "配置方式2_使用虚拟环境": {"mcpServers": {"word-document-reader": {"name": "Word文档内容读取", "type": "stdio", "description": "提供Word文档内容读取相关功能", "isActive": true, "registryUrl": "", "command": "/Users/<USER>/Cursor/skills/python/Word文档内容读取/.venv/bin/python", "args": ["-m", "word_document_reader.main"]}}}, "配置方式3_使用系统Python": {"mcpServers": {"word-document-reader": {"name": "Word文档内容读取", "type": "stdio", "description": "提供Word文档内容读取相关功能", "isActive": true, "registryUrl": "", "command": "python", "args": ["-m", "word_document_reader.main"], "env": {"PYTHONPATH": "/Users/<USER>/Cursor/skills/python/Word文档内容读取"}}}}, "Windows系统配置示例": {"mcpServers": {"word-document-reader": {"name": "Word文档内容读取", "type": "stdio", "description": "提供Word文档内容读取相关功能", "isActive": true, "registryUrl": "", "command": "uv", "args": ["--directory", "C:\\Users\\<USER>\\path\\to\\python\\Word文档内容读取", "run", "python", "-m", "word_document_reader.main"]}}}, "完整的Claude_Desktop配置示例": {"mcpServers": {"word-document-reader": {"name": "Word文档内容读取", "type": "stdio", "description": "提供Word文档内容读取相关功能", "isActive": true, "registryUrl": "", "command": "uv", "args": ["--directory", "/Users/<USER>/Cursor/skills/python/Word文档内容读取", "run", "python", "-m", "word_document_reader.main"]}, "word-footnote-management": {"name": "Word脚注管理", "type": "stdio", "description": "提供Word文档的脚注和尾注相关功能", "isActive": true, "registryUrl": "", "command": "uv", "args": ["--directory", "/Users/<USER>/Cursor/skills/python/word脚注管理", "run", "python", "-m", "word_footnote_management.main"]}}}, "_usage_instructions": {"步骤1": "选择上述配置方式之一", "步骤2": "修改路径为您的实际路径", "步骤3": "将mcpServers部分复制到Claude Desktop配置文件", "步骤4": "重启<PERSON>", "注意事项": "确保已安装uv包管理器，如果没有安装请使用配置方式2或3", "配置文件位置": {"Windows": "%APPDATA%\\Claude\\claude_desktop_config.json", "macOS": "~/Library/Application Support/Claude/claude_desktop_config.json", "Linux": "~/.config/claude/claude_desktop_config.json"}}}
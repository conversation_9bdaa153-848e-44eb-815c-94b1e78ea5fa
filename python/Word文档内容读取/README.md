# Word文档内容读取工具

这是一个专门用于读取Word文档内容的Python工具包，提供了五个核心功能函数。

## 功能特性

- **获取文档信息**: 提取文档的元数据信息（标题、作者、创建时间等）
- **提取文档文本**: 获取文档中的所有文本内容
- **获取文档大纲**: 获取文档的结构信息（段落和表格概览）
- **获取段落文本**: 提取指定段落的详细信息
- **查找文本**: 在文档中搜索指定文本并返回位置信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 导入模块

```python
from document_reader import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)
```

### 1. 获取文档信息

```python
# 获取文档的基本信息
info = get_document_info("example.docx")
print(info)
```

返回JSON格式的文档信息，包括：
- 标题、作者、主题、关键词
- 创建时间、修改时间、最后修改者
- 页数、字数、段落数、表格数

### 2. 提取文档文本

```python
# 提取文档中的所有文本
text = get_document_text("example.docx")
print(text)
```

返回文档中所有段落和表格的文本内容。

### 3. 获取文档大纲

```python
# 获取文档结构概览
outline = get_document_outline("example.docx")
print(outline)
```

返回JSON格式的文档结构，包括：
- 所有段落的索引、预览文本和样式
- 所有表格的索引、行列数和预览数据

### 4. 获取段落文本

```python
# 获取第一个段落的详细信息
paragraph_info = get_paragraph_text_from_document("example.docx", 0)
print(paragraph_info)
```

返回指定段落的详细信息：
- 段落索引和完整文本
- 段落样式名称
- 是否为标题段落

### 5. 查找文本

```python
# 在文档中查找文本
results = find_text_in_document("example.docx", "搜索关键词", match_case=True, whole_word=False)
print(results)
```

参数说明：
- `match_case`: 是否区分大小写
- `whole_word`: 是否只匹配完整单词

返回搜索结果，包括：
- 搜索查询信息
- 所有匹配位置的段落索引和上下文
- 总匹配数量

## 示例代码

```python
from document_reader import *

# 示例文档路径
doc_path = "sample.docx"

# 1. 获取文档基本信息
print("=== 文档信息 ===")
info = get_document_info(doc_path)
print(info)

# 2. 提取所有文本
print("\n=== 文档文本 ===")
text = get_document_text(doc_path)
print(text[:200] + "..." if len(text) > 200 else text)

# 3. 获取文档结构
print("\n=== 文档大纲 ===")
outline = get_document_outline(doc_path)
print(outline)

# 4. 获取特定段落
print("\n=== 第一段内容 ===")
paragraph = get_paragraph_text_from_document(doc_path, 0)
print(paragraph)

# 5. 搜索文本
print("\n=== 搜索结果 ===")
search_results = find_text_in_document(doc_path, "重要", match_case=False)
print(search_results)
```

## 错误处理

所有函数都包含完善的错误处理机制：
- 文件不存在时返回相应错误信息
- 无效参数时返回参数错误提示
- 处理异常时返回详细错误描述

## 注意事项

1. 支持的文件格式：`.docx`（如果传入的文件名没有扩展名，会自动添加`.docx`）
2. 段落索引从0开始计数
3. 搜索功能支持区分大小写和全词匹配选项
4. 返回的JSON数据使用UTF-8编码，支持中文显示

## 依赖库

- `python-docx`: 用于读取和处理Word文档

## 版本信息

- 版本: 1.0.0
- 作者: Word Document Reader

# Word文档内容读取MCP服务

这是一个基于MCP（Model Context Protocol）的Word文档内容读取服务，提供了五个核心的文档读取功能。

## 功能特性

- **获取文档信息**: 提取文档的元数据信息（标题、作者、创建时间等）
- **提取文档文本**: 获取文档中的所有文本内容
- **获取文档大纲**: 获取文档的结构信息（段落和表格概览）
- **获取段落文本**: 提取指定段落的详细信息
- **查找文本**: 在文档中搜索指定文本并返回位置信息

## 安装和配置

### 快速配置（推荐）

运行自动配置脚本：

```bash
cd python/Word文档内容读取
python setup_mcp.py
```

脚本会引导您完成安装和配置过程。

### 手动安装

#### 使用 uv（推荐）

```bash
cd python/Word文档内容读取
uv sync
```

#### 使用 pip

```bash
cd python/Word文档内容读取
pip install -e .
```

### Claude Desktop配置

#### 方式1: 使用uv（推荐）

在Claude Desktop配置文件中添加：

```json
{
  "mcpServers": {
    "word-document-reader": {
      "name": "Word文档内容读取",
      "type": "stdio",
      "description": "提供Word文档内容读取相关功能",
      "isActive": true,
      "registryUrl": "",
      "command": "uv",
      "args": [
        "--directory",
        "/Users/<USER>/Cursor/skills/python/Word文档内容读取",
        "run",
        "python",
        "-m",
        "word_document_reader.main"
      ]
    }
  }
}
```

#### 方式2: 使用虚拟环境

```json
{
  "mcpServers": {
    "word-document-reader": {
      "command": "/path/to/python/Word文档内容读取/.venv/bin/python",
      "args": ["/path/to/python/Word文档内容读取/word_document_reader/main.py"],
      "env": {
        "PYTHONPATH": "/path/to/python/Word文档内容读取"
      }
    }
  }
}
```

#### 方式3: 从PyPI安装后使用

```json
{
  "mcpServers": {
    "word-document-reader": {
      "command": "python",
      "args": ["-m", "word_document_reader.main"]
    }
  }
}
```

### Claude Desktop配置文件位置

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/claude/claude_desktop_config.json`

## 使用方法

### 作为MCP服务运行

```bash
word-document-reader
```

### 直接调用函数

```python
import asyncio
from word_document_reader.tools import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)

async def main():
    # 获取文档信息
    info = await get_document_info("example.docx")
    print(info)
    
    # 提取文档文本
    text = await get_document_text("example.docx")
    print(text)
    
    # 获取文档大纲
    outline = await get_document_outline("example.docx")
    print(outline)
    
    # 获取特定段落
    paragraph = await get_paragraph_text_from_document("example.docx", 0)
    print(paragraph)
    
    # 搜索文本
    results = await find_text_in_document("example.docx", "关键词")
    print(results)

asyncio.run(main())
```

## API 参考

### get_document_info(filename: str) -> str

获取Word文档的基本信息。

**参数:**
- `filename`: Word文档路径

**返回:** JSON格式的文档信息，包括：
- 标题、作者、主题、关键词
- 创建时间、修改时间、最后修改者
- 页数、字数、段落数、表格数

### get_document_text(filename: str) -> str

提取文档中的所有文本内容。

**参数:**
- `filename`: Word文档路径

**返回:** 文档的完整文本内容

### get_document_outline(filename: str) -> str

获取文档的结构概览。

**参数:**
- `filename`: Word文档路径

**返回:** JSON格式的文档结构，包括段落和表格信息

### get_paragraph_text_from_document(filename: str, paragraph_index: int) -> str

获取指定段落的详细信息。

**参数:**
- `filename`: Word文档路径
- `paragraph_index`: 段落索引（从0开始）

**返回:** JSON格式的段落信息

### find_text_in_document(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> str

在文档中搜索指定文本。

**参数:**
- `filename`: Word文档路径
- `text_to_find`: 要搜索的文本
- `match_case`: 是否区分大小写
- `whole_word`: 是否只匹配完整单词

**返回:** JSON格式的搜索结果

## 项目结构

```
word_document_reader/
├── __init__.py          # 包初始化
├── main.py             # MCP服务主程序
├── tools.py            # 核心工具函数
└── utils.py            # 辅助工具函数
```

## 依赖项

- `python-docx>=1.1.0`: Word文档处理
- `fastmcp>=2.8.1`: MCP服务框架

## 错误处理

所有函数都包含完善的错误处理机制：
- 文件不存在时返回相应错误信息
- 无效参数时返回参数错误提示
- 处理异常时返回详细错误描述

## 注意事项

1. 支持的文件格式：`.docx`（如果传入的文件名没有扩展名，会自动添加`.docx`）
2. 段落索引从0开始计数
3. 搜索功能支持区分大小写和全词匹配选项
4. 返回的JSON数据使用UTF-8编码，支持中文显示

## 许可证

MIT License

"""
Word文档内容读取MCP服务主程序

提供Word文档内容读取功能的MCP服务器
"""

import asyncio
from fastmcp import FastMCP
from .tools import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)

# 创建MCP服务器实例
mcp = FastMCP("Word文档内容读取")

# 注册工具函数
mcp.add_tool(get_document_info)
mcp.add_tool(get_document_text)
mcp.add_tool(get_document_outline)
mcp.add_tool(get_paragraph_text_from_document)
mcp.add_tool(find_text_in_document)


def main():
    """启动MCP服务器"""
    print("启动Word文档内容读取MCP服务...")
    print("提供以下功能:")
    print("- get_document_info: 获取文档信息")
    print("- get_document_text: 提取文档文本")
    print("- get_document_outline: 获取文档大纲")
    print("- get_paragraph_text_from_document: 获取段落文本")
    print("- find_text_in_document: 查找文档文本")
    
    # 运行MCP服务器
    mcp.run()


if __name__ == "__main__":
    main()

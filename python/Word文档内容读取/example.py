"""
Word文档内容读取工具使用示例

演示如何使用五个核心函数来读取Word文档内容
"""

from document_reader import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)


def demo_document_reading(doc_path: str):
    """演示文档读取功能"""
    
    print(f"=== 演示文档: {doc_path} ===\n")
    
    # 1. 获取文档基本信息
    print("1. 获取文档信息:")
    print("-" * 40)
    info = get_document_info(doc_path)
    print(info)
    print()
    
    # 2. 提取所有文本内容
    print("2. 提取文档文本:")
    print("-" * 40)
    text = get_document_text(doc_path)
    # 只显示前300个字符作为预览
    preview_text = text[:300] + "..." if len(text) > 300 else text
    print(preview_text)
    print(f"\n总文本长度: {len(text)} 字符")
    print()
    
    # 3. 获取文档结构大纲
    print("3. 获取文档大纲:")
    print("-" * 40)
    outline = get_document_outline(doc_path)
    print(outline)
    print()
    
    # 4. 获取特定段落内容
    print("4. 获取段落文本:")
    print("-" * 40)
    # 获取前3个段落的内容
    for i in range(3):
        paragraph_info = get_paragraph_text_from_document(doc_path, i)
        print(f"段落 {i}:")
        print(paragraph_info)
        print()
    
    # 5. 搜索文本功能演示
    print("5. 文本搜索功能:")
    print("-" * 40)
    
    # 搜索示例1: 区分大小写搜索
    search_term1 = "文档"
    results1 = find_text_in_document(doc_path, search_term1, match_case=True, whole_word=False)
    print(f"搜索 '{search_term1}' (区分大小写):")
    print(results1)
    print()
    
    # 搜索示例2: 不区分大小写搜索
    search_term2 = "WORD"
    results2 = find_text_in_document(doc_path, search_term2, match_case=False, whole_word=False)
    print(f"搜索 '{search_term2}' (不区分大小写):")
    print(results2)
    print()
    
    # 搜索示例3: 全词匹配搜索
    search_term3 = "文档"
    results3 = find_text_in_document(doc_path, search_term3, match_case=True, whole_word=True)
    print(f"搜索 '{search_term3}' (全词匹配):")
    print(results3)
    print()


def create_sample_document():
    """创建一个示例文档用于测试"""
    from docx import Document
    
    doc = Document()
    
    # 添加标题
    doc.add_heading('示例文档标题', 0)
    
    # 添加段落
    doc.add_paragraph('这是第一个段落，包含一些示例文本内容。')
    doc.add_paragraph('这是第二个段落，用于演示文档读取功能。')
    
    # 添加子标题
    doc.add_heading('子标题', level=1)
    
    # 添加更多段落
    doc.add_paragraph('这里是更多的文档内容，用于测试搜索功能。')
    doc.add_paragraph('Word文档处理是一个常见的需求。')
    
    # 添加表格
    table = doc.add_table(rows=2, cols=2)
    table.cell(0, 0).text = '表格标题1'
    table.cell(0, 1).text = '表格标题2'
    table.cell(1, 0).text = '数据1'
    table.cell(1, 1).text = '数据2'
    
    # 保存文档
    sample_path = 'sample_document.docx'
    doc.save(sample_path)
    print(f"已创建示例文档: {sample_path}")
    return sample_path


if __name__ == "__main__":
    # 创建示例文档
    sample_doc = create_sample_document()
    
    # 演示文档读取功能
    demo_document_reading(sample_doc)
    
    print("演示完成！")
    print("\n使用说明:")
    print("1. 将你的Word文档放在同一目录下")
    print("2. 修改doc_path变量为你的文档路径")
    print("3. 运行脚本查看结果")

#!/usr/bin/env python3
"""
Word文档内容读取MCP服务测试脚本

测试MCP服务的所有功能是否正常工作
"""

import asyncio
import os
import tempfile
from docx import Document
from word_document_reader.tools import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)


def create_test_document():
    """创建测试用的Word文档"""
    doc = Document()
    
    # 设置文档属性
    doc.core_properties.title = "MCP服务测试文档"
    doc.core_properties.author = "MCP测试"
    doc.core_properties.subject = "服务功能测试"
    doc.core_properties.keywords = "MCP,测试,Word"
    
    # 添加内容
    doc.add_heading('MCP服务测试文档', 0)
    doc.add_paragraph('这是第一个测试段落，用于验证MCP服务功能。')
    doc.add_paragraph('这里包含一些重要信息，需要通过搜索功能找到。')
    doc.add_paragraph('This is an English paragraph for testing multilingual support.')
    
    doc.add_heading('第二章节', level=1)
    doc.add_paragraph('章节内容包含更多测试数据。')
    doc.add_paragraph('重要提示：这是一个关键段落，包含特殊标记。')
    
    # 添加表格
    table = doc.add_table(rows=2, cols=2)
    table.cell(0, 0).text = '功能'
    table.cell(0, 1).text = '状态'
    table.cell(1, 0).text = 'MCP服务'
    table.cell(1, 1).text = '正常运行'
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
    doc.save(temp_file.name)
    return temp_file.name


async def test_get_document_info(doc_path):
    """测试获取文档信息功能"""
    print("=== 测试 get_document_info ===")
    try:
        result = await get_document_info(doc_path)
        print("✅ 成功获取文档信息")
        print(f"结果预览: {result[:200]}...")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_get_document_text(doc_path):
    """测试提取文档文本功能"""
    print("\n=== 测试 get_document_text ===")
    try:
        result = await get_document_text(doc_path)
        print("✅ 成功提取文档文本")
        print(f"文本长度: {len(result)} 字符")
        print(f"文本预览: {result[:100]}...")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_get_document_outline(doc_path):
    """测试获取文档大纲功能"""
    print("\n=== 测试 get_document_outline ===")
    try:
        result = await get_document_outline(doc_path)
        print("✅ 成功获取文档大纲")
        print(f"结果预览: {result[:200]}...")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_get_paragraph_text_from_document(doc_path):
    """测试获取段落文本功能"""
    print("\n=== 测试 get_paragraph_text_from_document ===")
    try:
        # 测试获取第一个段落
        result = await get_paragraph_text_from_document(doc_path, 0)
        print("✅ 成功获取段落文本")
        print(f"第0个段落: {result[:150]}...")
        
        # 测试无效索引
        result_invalid = await get_paragraph_text_from_document(doc_path, 999)
        if "error" in result_invalid.lower():
            print("✅ 正确处理无效索引")
        else:
            print("⚠️  无效索引处理可能有问题")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_find_text_in_document(doc_path):
    """测试文本搜索功能"""
    print("\n=== 测试 find_text_in_document ===")
    try:
        # 测试搜索存在的文本
        result1 = await find_text_in_document(doc_path, "测试", match_case=True, whole_word=False)
        print("✅ 成功搜索文本 '测试'")
        print(f"搜索结果预览: {result1[:200]}...")
        
        # 测试搜索不存在的文本
        result2 = await find_text_in_document(doc_path, "不存在的内容", match_case=True, whole_word=False)
        print("✅ 成功处理不存在的文本搜索")
        
        # 测试大小写不敏感搜索
        result3 = await find_text_in_document(doc_path, "ENGLISH", match_case=False, whole_word=False)
        print("✅ 成功进行大小写不敏感搜索")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    try:
        # 测试不存在的文件
        result = await get_document_info("不存在的文件.docx")
        if "does not exist" in result:
            print("✅ 正确处理文件不存在的情况")
        else:
            print("⚠️  文件不存在的错误处理可能有问题")
        
        # 测试空搜索文本
        test_file = create_test_document()
        result = await find_text_in_document(test_file, "", match_case=True, whole_word=False)
        if "cannot be empty" in result:
            print("✅ 正确处理空搜索文本")
        else:
            print("⚠️  空搜索文本的错误处理可能有问题")
        
        # 清理测试文件
        os.unlink(test_file)
        return True
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("Word文档内容读取MCP服务功能测试")
    print("=" * 50)
    
    # 创建测试文档
    test_file = create_test_document()
    print(f"✅ 已创建测试文档: {test_file}")
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(await test_get_document_info(test_file))
        test_results.append(await test_get_document_text(test_file))
        test_results.append(await test_get_document_outline(test_file))
        test_results.append(await test_get_paragraph_text_from_document(test_file))
        test_results.append(await test_find_text_in_document(test_file))
        test_results.append(await test_error_handling())
        
        # 统计测试结果
        passed = sum(test_results)
        total = len(test_results)
        
        print(f"\n{'='*50}")
        print(f"测试完成: {passed}/{total} 项测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！MCP服务功能正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能")
            return False
            
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.unlink(test_file)
            print(f"✅ 已清理测试文档: {test_file}")


def main():
    """主函数"""
    try:
        result = asyncio.run(run_all_tests())
        exit_code = 0 if result else 1
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        exit(1)
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        exit(1)


if __name__ == "__main__":
    main()

"""
Word文档内容读取工具的测试脚本

测试所有五个核心函数的功能
"""

import os
import json
from docx import Document
from document_reader import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)


def create_test_document():
    """创建测试用的Word文档"""
    doc = Document()
    
    # 设置文档属性
    doc.core_properties.title = "测试文档"
    doc.core_properties.author = "测试作者"
    doc.core_properties.subject = "文档读取测试"
    doc.core_properties.keywords = "测试,Word,文档"
    
    # 添加内容
    doc.add_heading('测试文档标题', 0)
    doc.add_paragraph('这是第一个测试段落，包含一些中文内容。')
    doc.add_paragraph('This is an English paragraph for testing.')
    doc.add_paragraph('这里有一些重要的信息需要查找。')
    
    doc.add_heading('第二章节', level=1)
    doc.add_paragraph('章节内容包含更多测试文本。')
    doc.add_paragraph('重要提示：这是一个重要的段落。')
    
    # 添加表格
    table = doc.add_table(rows=3, cols=2)
    table.cell(0, 0).text = '项目'
    table.cell(0, 1).text = '描述'
    table.cell(1, 0).text = '测试项1'
    table.cell(1, 1).text = '这是测试数据'
    table.cell(2, 0).text = '测试项2'
    table.cell(2, 1).text = '更多测试信息'
    
    # 保存文档
    test_file = 'test_document.docx'
    doc.save(test_file)
    return test_file


def test_get_document_info(doc_path):
    """测试获取文档信息功能"""
    print("=== 测试 get_document_info ===")
    result = get_document_info(doc_path)
    print("返回结果:")
    print(result)
    
    # 验证返回的是有效JSON
    try:
        data = json.loads(result)
        assert "title" in data
        assert "author" in data
        assert "paragraph_count" in data
        print("✓ 测试通过：返回有效的JSON格式文档信息")
    except:
        print("✗ 测试失败：返回格式不正确")
    print()


def test_get_document_text(doc_path):
    """测试提取文档文本功能"""
    print("=== 测试 get_document_text ===")
    result = get_document_text(doc_path)
    print("返回结果预览:")
    print(result[:200] + "..." if len(result) > 200 else result)
    
    # 验证包含预期内容
    if "测试文档标题" in result and "第一个测试段落" in result:
        print("✓ 测试通过：成功提取文档文本")
    else:
        print("✗ 测试失败：文本内容不完整")
    print()


def test_get_document_outline(doc_path):
    """测试获取文档大纲功能"""
    print("=== 测试 get_document_outline ===")
    result = get_document_outline(doc_path)
    print("返回结果:")
    print(result)
    
    # 验证返回的是有效JSON
    try:
        data = json.loads(result)
        assert "paragraphs" in data
        assert "tables" in data
        assert len(data["paragraphs"]) > 0
        print("✓ 测试通过：返回有效的文档结构信息")
    except:
        print("✗ 测试失败：结构信息格式不正确")
    print()


def test_get_paragraph_text_from_document(doc_path):
    """测试获取段落文本功能"""
    print("=== 测试 get_paragraph_text_from_document ===")
    
    # 测试获取第一个段落
    result = get_paragraph_text_from_document(doc_path, 0)
    print("获取第0个段落:")
    print(result)
    
    try:
        data = json.loads(result)
        assert "index" in data
        assert "text" in data
        assert "style" in data
        print("✓ 测试通过：成功获取段落信息")
    except:
        print("✗ 测试失败：段落信息格式不正确")
    
    # 测试无效索引
    result_invalid = get_paragraph_text_from_document(doc_path, 999)
    print("\n测试无效索引(999):")
    print(result_invalid)
    if "error" in result_invalid:
        print("✓ 测试通过：正确处理无效索引")
    else:
        print("✗ 测试失败：未正确处理无效索引")
    print()


def test_find_text_in_document(doc_path):
    """测试文本搜索功能"""
    print("=== 测试 find_text_in_document ===")
    
    # 测试1: 搜索存在的文本
    result1 = find_text_in_document(doc_path, "测试", match_case=True, whole_word=False)
    print("搜索'测试':")
    print(result1)
    
    try:
        data1 = json.loads(result1)
        assert "query" in data1
        assert "occurrences" in data1
        assert "total_count" in data1
        assert data1["total_count"] > 0
        print("✓ 测试通过：成功找到文本")
    except:
        print("✗ 测试失败：搜索结果格式不正确")
    
    # 测试2: 搜索不存在的文本
    result2 = find_text_in_document(doc_path, "不存在的文本", match_case=True, whole_word=False)
    print("\n搜索'不存在的文本':")
    print(result2)
    
    try:
        data2 = json.loads(result2)
        assert data2["total_count"] == 0
        print("✓ 测试通过：正确处理未找到的情况")
    except:
        print("✗ 测试失败：未找到文本的处理不正确")
    
    # 测试3: 测试大小写敏感性
    result3 = find_text_in_document(doc_path, "ENGLISH", match_case=False, whole_word=False)
    print("\n搜索'ENGLISH'(不区分大小写):")
    print(result3)
    
    try:
        data3 = json.loads(result3)
        if data3["total_count"] > 0:
            print("✓ 测试通过：大小写不敏感搜索工作正常")
        else:
            print("✗ 测试失败：大小写不敏感搜索未找到预期结果")
    except:
        print("✗ 测试失败：大小写搜索结果格式不正确")
    print()


def test_error_handling():
    """测试错误处理"""
    print("=== 测试错误处理 ===")
    
    # 测试不存在的文件
    result = get_document_info("不存在的文件.docx")
    print("测试不存在的文件:")
    print(result)
    if "does not exist" in result:
        print("✓ 测试通过：正确处理文件不存在的情况")
    else:
        print("✗ 测试失败：未正确处理文件不存在的情况")
    
    # 测试空搜索文本
    test_file = create_test_document()
    result = find_text_in_document(test_file, "", match_case=True, whole_word=False)
    print("\n测试空搜索文本:")
    print(result)
    if "cannot be empty" in result:
        print("✓ 测试通过：正确处理空搜索文本")
    else:
        print("✗ 测试失败：未正确处理空搜索文本")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    print()


def run_all_tests():
    """运行所有测试"""
    print("开始运行Word文档内容读取工具测试...\n")
    
    # 创建测试文档
    test_file = create_test_document()
    print(f"已创建测试文档: {test_file}\n")
    
    try:
        # 运行各项测试
        test_get_document_info(test_file)
        test_get_document_text(test_file)
        test_get_document_outline(test_file)
        test_get_paragraph_text_from_document(test_file)
        test_find_text_in_document(test_file)
        test_error_handling()
        
        print("所有测试完成！")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"已清理测试文档: {test_file}")


if __name__ == "__main__":
    run_all_tests()

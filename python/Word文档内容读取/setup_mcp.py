#!/usr/bin/env python3
"""
Word文档内容读取MCP服务配置设置脚本

自动生成MCP配置文件，支持多种安装方式
"""

import os
import sys
import json
import subprocess
import shutil
from pathlib import Path


def check_python_version():
    """检查Python版本是否满足要求"""
    if sys.version_info < (3, 10):
        print("错误: 需要Python 3.10或更高版本")
        print(f"当前版本: {sys.version}")
        sys.exit(1)


def check_uv_installed():
    """检查是否安装了uv"""
    try:
        subprocess.run(['uv', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def setup_venv():
    """设置虚拟环境并安装依赖"""
    base_path = os.path.abspath(os.path.dirname(__file__))
    venv_path = os.path.join(base_path, '.venv')
    
    print("正在设置虚拟环境...")
    
    # 创建虚拟环境
    subprocess.run([sys.executable, '-m', 'venv', venv_path], check=True)
    
    # 确定Python可执行文件路径
    if os.name == 'nt':  # Windows
        python_path = os.path.join(venv_path, 'Scripts', 'python.exe')
        pip_path = os.path.join(venv_path, 'Scripts', 'pip.exe')
    else:  # Unix/Linux/macOS
        python_path = os.path.join(venv_path, 'bin', 'python')
        pip_path = os.path.join(venv_path, 'bin', 'pip')
    
    # 升级pip
    subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'], check=True)
    
    # 安装项目依赖
    subprocess.run([pip_path, 'install', '-e', '.'], check=True)
    
    print(f"虚拟环境设置完成: {venv_path}")
    return python_path


def generate_mcp_config_uv():
    """生成使用uv的MCP配置（推荐）"""
    base_path = os.path.abspath(os.path.dirname(__file__))
    
    config = {
        "mcpServers": {
            "word-document-reader": {
                "name": "Word文档内容读取",
                "type": "stdio",
                "description": "提供Word文档内容读取相关功能",
                "isActive": True,
                "registryUrl": "",
                "command": "uv",
                "args": [
                    "--directory",
                    base_path,
                    "run",
                    "python",
                    "-m",
                    "word_document_reader.main"
                ]
            }
        }
    }
    
    # 保存配置到JSON文件
    config_path = os.path.join(base_path, 'mcp-config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return config_path


def generate_mcp_config_venv(python_path):
    """生成使用虚拟环境的MCP配置"""
    base_path = os.path.abspath(os.path.dirname(__file__))
    
    config = {
        "mcpServers": {
            "word-document-reader": {
                "name": "Word文档内容读取",
                "type": "stdio",
                "description": "提供Word文档内容读取相关功能",
                "isActive": True,
                "registryUrl": "",
                "command": python_path,
                "args": [
                    "-m",
                    "word_document_reader.main"
                ]
            }
        }
    }
    
    # 保存配置到JSON文件
    config_path = os.path.join(base_path, 'mcp-config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return config_path


def generate_mcp_config_system():
    """生成使用系统Python的MCP配置"""
    base_path = os.path.abspath(os.path.dirname(__file__))
    
    config = {
        "mcpServers": {
            "word-document-reader": {
                "name": "Word文档内容读取",
                "type": "stdio",
                "description": "提供Word文档内容读取相关功能",
                "isActive": True,
                "registryUrl": "",
                "command": "python",
                "args": [
                    "-m",
                    "word_document_reader.main"
                ],
                "env": {
                    "PYTHONPATH": base_path
                }
            }
        }
    }
    
    # 保存配置到JSON文件
    config_path = os.path.join(base_path, 'mcp-config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    return config_path


def print_config_instructions(config_path):
    """打印配置说明"""
    print(f"\n✅ MCP配置文件已生成: {config_path}")
    print("\n📋 配置内容:")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_content = f.read()
        print(config_content)
    
    print("\n🔧 Claude Desktop配置说明:")
    print("1. 找到Claude Desktop的配置文件:")
    
    if os.name == 'nt':  # Windows
        config_dir = os.path.expanduser("~\\AppData\\Roaming\\Claude\\")
    elif sys.platform == 'darwin':  # macOS
        config_dir = os.path.expanduser("~/Library/Application Support/Claude/")
    else:  # Linux
        config_dir = os.path.expanduser("~/.config/claude/")
    
    claude_config_path = os.path.join(config_dir, "claude_desktop_config.json")
    print(f"   {claude_config_path}")
    
    print("\n2. 将以上配置内容添加到Claude Desktop配置文件中")
    print("   如果文件不存在，请创建一个新文件")
    print("   如果文件已存在，请将mcpServers部分合并到现有配置中")
    
    print(f"\n3. 重启Claude Desktop以加载新配置")
    print(f"\n4. 配置完成后，您可以在Claude中使用以下功能:")
    print("   - get_document_info: 获取文档信息")
    print("   - get_document_text: 提取文档文本")
    print("   - get_document_outline: 获取文档大纲")
    print("   - get_paragraph_text_from_document: 获取段落文本")
    print("   - find_text_in_document: 查找文档文本")


def main():
    """主函数"""
    print("Word文档内容读取MCP服务配置设置")
    print("=" * 50)
    
    check_python_version()
    
    uv_installed = check_uv_installed()
    
    print("\n请选择配置方式:")
    if uv_installed:
        print("1. 使用uv（推荐，自动管理依赖）")
        print("2. 使用虚拟环境")
        print("3. 使用系统Python")
    else:
        print("1. 使用虚拟环境（推荐）")
        print("2. 使用系统Python")
        print("\n注意: 未检测到uv，建议安装uv以获得更好的依赖管理体验")
        print("安装uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
    
    choice = input(f"\n请输入选择 (1-{3 if uv_installed else 2}): ").strip()
    
    try:
        if uv_installed and choice == "1":
            # 使用uv
            config_path = generate_mcp_config_uv()
            print_config_instructions(config_path)
            
        elif (uv_installed and choice == "2") or (not uv_installed and choice == "1"):
            # 使用虚拟环境
            python_path = setup_venv()
            config_path = generate_mcp_config_venv(python_path)
            print_config_instructions(config_path)
            
        elif (uv_installed and choice == "3") or (not uv_installed and choice == "2"):
            # 使用系统Python
            config_path = generate_mcp_config_system()
            print_config_instructions(config_path)
            print("\n⚠️  注意: 请确保已安装项目依赖:")
            print("   pip install python-docx fastmcp")
            
        else:
            print("无效选择，退出")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n配置过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

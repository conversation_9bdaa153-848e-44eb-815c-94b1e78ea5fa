"""
Word文档内容读取工具包

提供Word文档内容读取的核心功能，包括：
- 获取文档信息
- 提取文档文本
- 获取文档大纲
- 获取特定段落文本
- 在文档中查找文本
"""

from .document_reader import (
    get_document_info,
    get_document_text,
    get_document_outline,
    get_paragraph_text_from_document,
    find_text_in_document
)

__version__ = "1.0.0"
__author__ = "Word Document Reader"

__all__ = [
    "get_document_info",
    "get_document_text", 
    "get_document_outline",
    "get_paragraph_text_from_document",
    "find_text_in_document"
]
